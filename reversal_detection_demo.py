#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
反转检测算法演示程序
直观展示高点低点的判断逻辑
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Rectangle
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def detect_reversals_with_details(prices, window=10, min_change=0.05):
    """
    检测反转点并返回详细信息
    """
    reversals = []
    candidates = []  # 候选反转点（包括被过滤的）
    
    for i in range(window, len(prices) - window):
        current_price = prices[i]
        
        # 检查是否为局部最高点（峰值）
        is_peak = True
        window_prices = []
        for j in range(i - window, i + window + 1):
            window_prices.append(prices[j])
            if j != i and prices[j] >= current_price:
                is_peak = False
        
        # 检查是否为局部最低点（谷值）
        is_valley = True
        for j in range(i - window, i + window + 1):
            if j != i and prices[j] <= current_price:
                is_valley = False
        
        if is_peak or is_valley:
            reversal_type = 'peak' if is_peak else 'valley'
            
            # 计算反转幅度
            prev_extreme = None
            for k in range(len(reversals) - 1, -1, -1):
                if reversals[k][1] != reversal_type:
                    prev_extreme = reversals[k][2]
                    break
            
            magnitude = 0
            is_valid = True
            
            if prev_extreme is not None:
                magnitude = abs(current_price - prev_extreme) / prev_extreme
                is_valid = magnitude >= min_change
            
            # 记录候选点
            candidates.append({
                'day': i,
                'type': reversal_type,
                'price': current_price,
                'magnitude': magnitude,
                'is_valid': is_valid,
                'window_prices': window_prices,
                'window_range': (i - window, i + window)
            })
            
            # 只有有效的反转点才加入最终结果
            if is_valid:
                reversals.append((i, reversal_type, current_price, magnitude))
    
    return reversals, candidates

def create_demo_price_series():
    """
    创建演示用的价格序列
    """
    np.random.seed(42)
    days = 100
    
    # 创建基础趋势
    trend = np.linspace(100, 120, days)
    
    # 添加周期性波动
    cycle1 = 10 * np.sin(np.linspace(0, 4*np.pi, days))
    cycle2 = 5 * np.sin(np.linspace(0, 8*np.pi, days))
    
    # 添加随机噪音
    noise = np.random.normal(0, 2, days)
    
    # 合成价格序列
    prices = trend + cycle1 + cycle2 + noise
    
    # 确保价格为正
    prices = np.maximum(prices, 50)
    
    return prices

def visualize_reversal_detection():
    """
    可视化反转检测过程
    """
    # 生成演示数据
    prices = create_demo_price_series()
    window = 8
    min_change = 0.05
    
    # 检测反转点
    reversals, candidates = detect_reversals_with_details(prices, window, min_change)
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12))
    
    # 上图：显示所有候选点和检测窗口
    ax1.plot(prices, 'b-', linewidth=1, alpha=0.7, label='价格序列')
    
    # 标记有效反转点
    valid_peaks = [(c['day'], c['price']) for c in candidates if c['is_valid'] and c['type'] == 'peak']
    valid_valleys = [(c['day'], c['price']) for c in candidates if c['is_valid'] and c['type'] == 'valley']
    
    if valid_peaks:
        peak_days, peak_prices = zip(*valid_peaks)
        ax1.scatter(peak_days, peak_prices, color='red', s=100, marker='^', 
                   label=f'有效顶部反转 ({len(valid_peaks)}个)', zorder=5)
    
    if valid_valleys:
        valley_days, valley_prices = zip(*valid_valleys)
        ax1.scatter(valley_days, valley_prices, color='green', s=100, marker='v', 
                   label=f'有效底部反转 ({len(valid_valleys)}个)', zorder=5)
    
    # 标记被过滤的候选点
    filtered_points = [(c['day'], c['price']) for c in candidates if not c['is_valid']]
    if filtered_points:
        filtered_days, filtered_prices = zip(*filtered_points)
        ax1.scatter(filtered_days, filtered_prices, color='gray', s=50, marker='o', 
                   alpha=0.5, label=f'被过滤的候选点 ({len(filtered_points)}个)')
    
    ax1.set_title(f'反转检测结果 (窗口={window}天, 最小幅度={min_change*100}%)', fontsize=14, fontweight='bold')
    ax1.set_xlabel('交易日')
    ax1.set_ylabel('价格')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 下图：详细展示几个反转点的检测过程
    ax2.plot(prices, 'b-', linewidth=1, alpha=0.7, label='价格序列')
    
    # 选择几个有代表性的反转点进行详细展示
    demo_points = [c for c in candidates if c['is_valid']][:4]  # 选择前4个有效反转点
    
    colors = ['red', 'green', 'orange', 'purple']
    
    for idx, point in enumerate(demo_points):
        day = point['day']
        price = point['price']
        window_range = point['window_range']
        color = colors[idx % len(colors)]
        
        # 绘制检测窗口
        rect = Rectangle((window_range[0], min(prices) - 5), 
                        window_range[1] - window_range[0], 
                        max(prices) - min(prices) + 10,
                        linewidth=2, edgecolor=color, facecolor=color, alpha=0.1)
        ax2.add_patch(rect)
        
        # 标记反转点
        marker = '^' if point['type'] == 'peak' else 'v'
        ax2.scatter(day, price, color=color, s=150, marker=marker, 
                   edgecolor='black', linewidth=2, zorder=5)
        
        # 添加标注
        ax2.annotate(f'{point["type"]}\n{point["magnitude"]*100:.1f}%', 
                    xy=(day, price), xytext=(10, 20), 
                    textcoords='offset points', fontsize=10,
                    bbox=dict(boxstyle='round,pad=0.3', facecolor=color, alpha=0.7),
                    arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
    
    ax2.set_title('反转点检测窗口详细展示', fontsize=14, fontweight='bold')
    ax2.set_xlabel('交易日')
    ax2.set_ylabel('价格')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # 打印统计信息
    print("=" * 60)
    print("反转检测统计信息")
    print("=" * 60)
    print(f"价格序列长度: {len(prices)}天")
    print(f"检测窗口大小: {window}天")
    print(f"最小反转幅度: {min_change*100}%")
    print(f"候选反转点总数: {len(candidates)}个")
    print(f"有效反转点数量: {len(reversals)}个")
    print(f"过滤率: {(len(candidates) - len(reversals))/len(candidates)*100:.1f}%")
    
    print("\n有效反转点详情:")
    print("-" * 40)
    for i, (day, rev_type, price, magnitude) in enumerate(reversals, 1):
        print(f"{i:2d}. 第{day:2d}天 {rev_type:6s} 价格:{price:6.2f} 幅度:{magnitude*100:5.1f}%")

def demonstrate_parameter_effects():
    """
    演示不同参数对检测结果的影响
    """
    prices = create_demo_price_series()
    
    # 不同的参数组合
    param_combinations = [
        (5, 0.03),   # 敏感设置
        (10, 0.05),  # 标准设置
        (15, 0.08),  # 保守设置
    ]
    
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    for idx, (window, min_change) in enumerate(param_combinations):
        ax = axes[idx]
        
        # 检测反转点
        reversals, candidates = detect_reversals_with_details(prices, window, min_change)
        
        # 绘制价格序列
        ax.plot(prices, 'b-', linewidth=1, alpha=0.7, label='价格序列')
        
        # 标记反转点
        valid_peaks = [(c['day'], c['price']) for c in candidates if c['is_valid'] and c['type'] == 'peak']
        valid_valleys = [(c['day'], c['price']) for c in candidates if c['is_valid'] and c['type'] == 'valley']
        
        if valid_peaks:
            peak_days, peak_prices = zip(*valid_peaks)
            ax.scatter(peak_days, peak_prices, color='red', s=80, marker='^', 
                      label=f'顶部反转 ({len(valid_peaks)}个)')
        
        if valid_valleys:
            valley_days, valley_prices = zip(*valid_valleys)
            ax.scatter(valley_days, valley_prices, color='green', s=80, marker='v', 
                      label=f'底部反转 ({len(valid_valleys)}个)')
        
        ax.set_title(f'窗口={window}天, 阈值={min_change*100}%\n检测到{len(reversals)}个反转点', 
                    fontsize=12, fontweight='bold')
        ax.set_xlabel('交易日')
        ax.set_ylabel('价格')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

def main():
    """
    主程序
    """
    print("=" * 60)
    print("股市反转检测算法演示")
    print("=" * 60)
    
    print("\n1. 基本反转检测演示")
    visualize_reversal_detection()
    
    print("\n" + "=" * 60)
    print("2. 参数影响对比演示")
    demonstrate_parameter_effects()
    
    print("\n演示完成！")

if __name__ == "__main__":
    main()
