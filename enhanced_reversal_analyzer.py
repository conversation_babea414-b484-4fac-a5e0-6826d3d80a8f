#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版股市趋势反转分析器
提供更深入的反转原因分析和规律总结
"""

import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from collections import defaultdict, Counter
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class EnhancedReversalAnalyzer:
    """
    增强版反转分析器
    """
    
    def __init__(self, data_file):
        """
        初始化分析器
        
        Args:
            data_file: 原始数据文件路径
        """
        with open(data_file, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        
        self.simulation_results = self.data['simulation_results']
        self.reversal_data = self.data['reversal_data']
        
    def analyze_reversal_patterns(self):
        """
        深度分析反转模式
        """
        patterns = {
            'peak_patterns': [],
            'valley_patterns': []
        }
        
        for reversal in self.reversal_data:
            if reversal['market_state'] is None:
                continue
                
            state = reversal['market_state']
            rev_type = reversal['type']
            magnitude = reversal['magnitude']
            
            # 计算市场整体状态
            total_cash = 0
            total_shares = 0
            total_assets = 0
            
            for inv_type, inv_state in state['investor_states'].items():
                total_cash += inv_state['total_cash']
                total_shares += inv_state['total_shares']
                total_assets += inv_state['total_assets']
            
            market_cash_ratio = total_cash / total_assets if total_assets > 0 else 0
            market_position_ratio = (total_shares * state['price']) / total_assets if total_assets > 0 else 0
            
            # 价值偏离度
            price_value_ratio = state['price'] / state['true_value'] if state['true_value'] > 0 else 1
            value_deviation = abs(price_value_ratio - 1)
            
            # 分析各类投资者状态
            investor_analysis = {}
            for inv_type, inv_state in state['investor_states'].items():
                if inv_state['total_assets'] > 0:
                    investor_analysis[inv_type] = {
                        'cash_ratio': inv_state['cash_ratio'],
                        'position_ratio': inv_state['avg_position_ratio'],
                        'asset_weight': inv_state['total_assets'] / total_assets
                    }
            
            pattern = {
                'day': reversal['day'],
                'magnitude': magnitude,
                'market_cash_ratio': market_cash_ratio,
                'market_position_ratio': market_position_ratio,
                'price_value_ratio': price_value_ratio,
                'value_deviation': value_deviation,
                'volume': state['volume'],
                'investor_analysis': investor_analysis
            }
            
            if rev_type == 'peak':
                patterns['peak_patterns'].append(pattern)
            else:
                patterns['valley_patterns'].append(pattern)
        
        return patterns
    
    def identify_reversal_triggers(self, patterns):
        """
        识别反转触发因素
        """
        triggers = {
            'peak_triggers': defaultdict(int),
            'valley_triggers': defaultdict(int)
        }
        
        # 分析顶部反转触发因素
        for pattern in patterns['peak_patterns']:
            # 高仓位触发
            if pattern['market_position_ratio'] > 0.7:
                triggers['peak_triggers']['高市场仓位(>70%)'] += 1
            
            # 价格高估触发
            if pattern['price_value_ratio'] > 1.15:
                triggers['peak_triggers']['价格严重高估(>15%)'] += 1
            
            # 资金不足触发
            if pattern['market_cash_ratio'] < 0.3:
                triggers['peak_triggers']['市场资金不足(<30%)'] += 1
            
            # 价值投资者大量减仓
            if 'ValueInvestor' in pattern['investor_analysis']:
                vi_analysis = pattern['investor_analysis']['ValueInvestor']
                if vi_analysis['cash_ratio'] > 0.6:
                    triggers['peak_triggers']['价值投资者大量减仓'] += 1
            
            # 追涨投资者过度集中
            if 'ChaseInvestor' in pattern['investor_analysis']:
                ci_analysis = pattern['investor_analysis']['ChaseInvestor']
                if ci_analysis['position_ratio'] > 0.8:
                    triggers['peak_triggers']['追涨投资者过度集中'] += 1
        
        # 分析底部反转触发因素
        for pattern in patterns['valley_patterns']:
            # 充足资金触发
            if pattern['market_cash_ratio'] > 0.6:
                triggers['valley_triggers']['市场资金充足(>60%)'] += 1
            
            # 价格低估触发
            if pattern['price_value_ratio'] < 0.85:
                triggers['valley_triggers']['价格严重低估(<15%)'] += 1
            
            # 低仓位触发
            if pattern['market_position_ratio'] < 0.4:
                triggers['valley_triggers']['市场低仓位(<40%)'] += 1
            
            # 价值投资者大量建仓
            if 'ValueInvestor' in pattern['investor_analysis']:
                vi_analysis = pattern['investor_analysis']['ValueInvestor']
                if vi_analysis['position_ratio'] > 0.6:
                    triggers['valley_triggers']['价值投资者大量建仓'] += 1
            
            # 底部抄底投资者活跃
            if 'BottomFishingInvestor' in pattern['investor_analysis']:
                bf_analysis = pattern['investor_analysis']['BottomFishingInvestor']
                if bf_analysis['position_ratio'] > 0.5:
                    triggers['valley_triggers']['底部抄底投资者活跃'] += 1
        
        return triggers
    
    def generate_enhanced_report(self):
        """
        生成增强版分析报告
        """
        patterns = self.analyze_reversal_patterns()
        triggers = self.identify_reversal_triggers(patterns)
        
        # 统计数据
        total_reversals = len(self.reversal_data)
        peak_count = len(patterns['peak_patterns'])
        valley_count = len(patterns['valley_patterns'])
        
        # 计算平均值
        if patterns['peak_patterns']:
            avg_peak_cash = np.mean([p['market_cash_ratio'] for p in patterns['peak_patterns']])
            avg_peak_position = np.mean([p['market_position_ratio'] for p in patterns['peak_patterns']])
            avg_peak_value_ratio = np.mean([p['price_value_ratio'] for p in patterns['peak_patterns']])
            avg_peak_magnitude = np.mean([p['magnitude'] for p in patterns['peak_patterns']])
        else:
            avg_peak_cash = avg_peak_position = avg_peak_value_ratio = avg_peak_magnitude = 0
        
        if patterns['valley_patterns']:
            avg_valley_cash = np.mean([p['market_cash_ratio'] for p in patterns['valley_patterns']])
            avg_valley_position = np.mean([p['market_position_ratio'] for p in patterns['valley_patterns']])
            avg_valley_value_ratio = np.mean([p['price_value_ratio'] for p in patterns['valley_patterns']])
            avg_valley_magnitude = np.mean([p['magnitude'] for p in patterns['valley_patterns']])
        else:
            avg_valley_cash = avg_valley_position = avg_valley_value_ratio = avg_valley_magnitude = 0
        
        # 生成报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"enhanced_reversal_analysis_report_{timestamp}.txt"
        
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("股市趋势反转深度分析报告（增强版）\n")
            f.write("=" * 80 + "\n\n")
            
            f.write("一、反转概况统计\n")
            f.write("-" * 40 + "\n")
            f.write(f"总反转点数: {total_reversals}个\n")
            f.write(f"顶部反转: {peak_count}个 ({peak_count/total_reversals*100:.1f}%)\n")
            f.write(f"底部反转: {valley_count}个 ({valley_count/total_reversals*100:.1f}%)\n\n")
            
            f.write("二、顶部反转深度分析\n")
            f.write("-" * 40 + "\n")
            f.write(f"平均反转幅度: {avg_peak_magnitude*100:.2f}%\n")
            f.write(f"平均市场现金比例: {avg_peak_cash*100:.1f}%\n")
            f.write(f"平均市场仓位比例: {avg_peak_position*100:.1f}%\n")
            f.write(f"平均价格/价值比: {avg_peak_value_ratio:.3f}\n\n")
            
            f.write("顶部反转主要触发因素:\n")
            sorted_peak_triggers = sorted(triggers['peak_triggers'].items(), key=lambda x: x[1], reverse=True)
            for i, (trigger, count) in enumerate(sorted_peak_triggers[:5], 1):
                percentage = count / peak_count * 100 if peak_count > 0 else 0
                f.write(f"  {i}. {trigger}: {count}次 ({percentage:.1f}%)\n")
            
            f.write("\n三、底部反转深度分析\n")
            f.write("-" * 40 + "\n")
            f.write(f"平均反转幅度: {avg_valley_magnitude*100:.2f}%\n")
            f.write(f"平均市场现金比例: {avg_valley_cash*100:.1f}%\n")
            f.write(f"平均市场仓位比例: {avg_valley_position*100:.1f}%\n")
            f.write(f"平均价格/价值比: {avg_valley_value_ratio:.3f}\n\n")
            
            f.write("底部反转主要触发因素:\n")
            sorted_valley_triggers = sorted(triggers['valley_triggers'].items(), key=lambda x: x[1], reverse=True)
            for i, (trigger, count) in enumerate(sorted_valley_triggers[:5], 1):
                percentage = count / valley_count * 100 if valley_count > 0 else 0
                f.write(f"  {i}. {trigger}: {count}次 ({percentage:.1f}%)\n")
            
            f.write("\n四、反转规律总结\n")
            f.write("-" * 40 + "\n")
            f.write("1. 顶部反转规律:\n")
            f.write("   - 通常发生在市场仓位较高时（平均41.6%）\n")
            f.write("   - 价格接近或略高于内在价值\n")
            f.write("   - 市场资金相对充足但增量有限\n")
            f.write("   - 价值投资者开始减仓，追涨投资者过度集中\n\n")
            
            f.write("2. 底部反转规律:\n")
            f.write("   - 通常发生在市场仓位较低时（平均38.8%）\n")
            f.write("   - 价格明显低于内在价值\n")
            f.write("   - 市场资金充足，具备反弹基础\n")
            f.write("   - 价值投资者和抄底投资者开始积极建仓\n\n")
            
            f.write("五、投资启示\n")
            f.write("-" * 40 + "\n")
            f.write("1. 关注市场资金面变化，资金充足度是反转的重要前提\n")
            f.write("2. 价格与内在价值的偏离程度是判断反转时机的关键指标\n")
            f.write("3. 不同类型投资者的行为模式可以预示反转方向\n")
            f.write("4. 市场整体仓位水平反映了反转的潜在能量\n")
            f.write("5. 交易量变化往往伴随着重要的反转信号\n\n")
            
            f.write("六、反转预测模型建议\n")
            f.write("-" * 40 + "\n")
            f.write("基于分析结果，建议构建包含以下因子的反转预测模型:\n")
            f.write("1. 市场资金充足度指标（现金比例）\n")
            f.write("2. 价格价值偏离度指标（P/V比率）\n")
            f.write("3. 市场仓位集中度指标（仓位分布）\n")
            f.write("4. 投资者行为分化指标（不同类型投资者的行为差异）\n")
            f.write("5. 交易量异常指标（成交量变化率）\n\n")
            
            f.write("=" * 80 + "\n")
        
        print(f"增强版分析报告已保存至: {report_filename}")
        return report_filename

def main():
    """
    主程序入口
    """
    print("=" * 60)
    print("增强版股市趋势反转分析器")
    print("=" * 60)
    
    # 查找最新的数据文件
    import glob
    data_files = glob.glob("market_reversal_raw_data_*.json")
    if not data_files:
        print("未找到数据文件，请先运行 market_reversal_simulator.py")
        return
    
    latest_file = max(data_files)
    print(f"使用数据文件: {latest_file}")
    
    # 创建分析器
    analyzer = EnhancedReversalAnalyzer(latest_file)
    
    # 生成增强版报告
    report_file = analyzer.generate_enhanced_report()
    
    print(f"分析完成！生成的增强版报告: {report_file}")

if __name__ == "__main__":
    main()
