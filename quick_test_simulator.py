#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试版本的股市趋势反转分析模拟器
"""

import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from collections import defaultdict, Counter
import json
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入主模拟程序
import sys
import importlib.util

# 动态导入main_OHLC_2.0.6.1.py
spec = importlib.util.spec_from_file_location("main_OHLC", "main_OHLC_2.0.6.1.py")
main_module = importlib.util.module_from_spec(spec)
sys.modules["main_OHLC"] = main_module
spec.loader.exec_module(main_module)

# 导入需要的类
TrueValueCurve = main_module.TrueValueCurve
Investor = main_module.Investor
ValueInvestor = main_module.ValueInvestor
ChaseInvestor = main_module.ChaseInvestor
TrendInvestor = main_module.TrendInvestor
RandomInvestor = main_module.RandomInvestor
Market = main_module.Market

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def detect_reversals(prices, window=5, min_change=0.03):
    """
    检测价格趋势反转点
    """
    reversals = []
    
    for i in range(window, len(prices) - window):
        current_price = prices[i]
        
        # 检查是否为局部最高点（峰值）
        is_peak = True
        for j in range(i - window, i + window + 1):
            if j != i and prices[j] >= current_price:
                is_peak = False
                break
        
        # 检查是否为局部最低点（谷值）
        is_valley = True
        for j in range(i - window, i + window + 1):
            if j != i and prices[j] <= current_price:
                is_valley = False
                break
        
        if is_peak or is_valley:
            # 计算反转幅度
            prev_extreme = None
            for k in range(len(reversals) - 1, -1, -1):
                if reversals[k][1] != ('peak' if is_peak else 'valley'):
                    prev_extreme = reversals[k][2]
                    break
            
            if prev_extreme is not None:
                magnitude = abs(current_price - prev_extreme) / prev_extreme
                if magnitude >= min_change:
                    reversal_type = 'peak' if is_peak else 'valley'
                    reversals.append((i, reversal_type, current_price, magnitude))
            else:
                # 第一个反转点
                reversal_type = 'peak' if is_peak else 'valley'
                reversals.append((i, reversal_type, current_price, 0.0))
    
    return reversals

def analyze_market_state(market, day):
    """
    分析市场状态
    """
    state = {
        'day': day,
        'price': market.price_history[day] if day < len(market.price_history) else 0,
        'volume': market.executed_volume_history[day] if day < len(market.executed_volume_history) else 0,
        'true_value': market.value_curve[day] if day < len(market.value_curve) else 0,
        'investor_states': {}
    }
    
    # 分析各类投资者状态
    investor_types = ['ValueInvestor', 'ChaseInvestor', 'TrendInvestor', 'RandomInvestor']
    
    for inv_type in investor_types:
        investors = [inv for inv in market.investors if inv.__class__.__name__ == inv_type]
        if investors:
            total_cash = sum(inv.cash for inv in investors)
            total_shares = sum(inv.shares for inv in investors)
            total_assets = total_cash + total_shares * state['price']
            avg_position_ratio = (total_shares * state['price']) / total_assets if total_assets > 0 else 0
            
            state['investor_states'][inv_type] = {
                'count': len(investors),
                'total_cash': total_cash,
                'total_shares': total_shares,
                'total_assets': total_assets,
                'avg_position_ratio': avg_position_ratio,
                'cash_ratio': total_cash / total_assets if total_assets > 0 else 0
            }
    
    return state

def run_single_simulation(seed):
    """
    运行单次模拟
    """
    print(f"运行模拟 {seed}...")
    
    # 设置随机种子
    np.random.seed(seed)
    
    # 基本市场参数设置（简化版本）
    initial_price = 100
    price_tick = 0.01
    days = 200  # 减少天数以加快测试
    buy_fee_rate = 0.001
    sell_fee_rate = 0.001
    
    # 禁用交易日志以提高性能
    Investor.set_enable_trade_log(False)
    
    # 创建价值曲线
    value_curve = TrueValueCurve(initial_value=initial_price, days=days, seed=seed)
    
    # 创建市场实例
    market = Market(initial_price, price_tick, value_curve=value_curve, seed=seed,
                   buy_fee_rate=buy_fee_rate, sell_fee_rate=sell_fee_rate)
    
    # 创建投资者（简化版本）
    investors = []
    
    # 创建价值投资者
    for i in range(20):
        bias_percent = np.random.normal(0, 0.15)
        max_deviation = np.random.uniform(0.2, 0.4)
        target_position = np.random.uniform(0.4, 0.6)
        investors.append(ValueInvestor(100, 10000, bias_percent=bias_percent,
                                     max_deviation=max_deviation, target_position=target_position,
                                     seed=seed + i))
    
    # 创建追涨杀跌投资者
    for i in range(20):
        investors.append(ChaseInvestor(100, 10000))
    
    # 创建趋势投资者
    for i in range(10):
        investors.append(TrendInvestor(100, 10000, 10))
    
    # 创建随机投资者
    for i in range(10):
        investors.append(RandomInvestor(100, 10000, seed=seed + i + 1000))
    
    # 将投资者添加到市场
    market.investors = investors
    
    # 记录市场状态
    market_states = []
    
    # 运行模拟
    for day in range(days):
        # 投资者决策和下单
        for investor in investors:
            investor.trade(market.price, market)
        
        # 执行交易
        market.daily_auction()
        
        # 每5天记录一次市场状态
        if day % 5 == 0:
            state = analyze_market_state(market, day)
            market_states.append(state)
    
    # 检测反转点
    reversals = detect_reversals(market.price_history)
    
    # 计算最终收益
    final_price = market.price_history[-1]
    initial_price = market.price_history[0]
    total_return = (final_price - initial_price) / initial_price
    
    result = {
        'seed': seed,
        'price_history': market.price_history,
        'volume_history': market.executed_volume_history,
        'value_history': market.value_curve.values,
        'reversals': reversals,
        'market_states': market_states,
        'total_return': total_return,
        'final_price': final_price,
        'max_price': max(market.price_history),
        'min_price': min(market.price_history),
        'volatility': np.std(market.price_history) / np.mean(market.price_history)
    }
    
    return result

def main():
    """
    主程序入口
    """
    print("=" * 60)
    print("股市趋势反转快速测试")
    print("=" * 60)
    
    # 运行3次快速测试
    simulation_count = 3
    results = []
    
    print(f"\n将进行 {simulation_count} 次快速测试...")
    
    for i in range(simulation_count):
        seed = i + 1000
        result = run_single_simulation(seed)
        results.append(result)
    
    print(f"\n完成 {simulation_count} 次测试")
    
    # 简单分析
    total_reversals = sum(len(r['reversals']) for r in results)
    avg_reversals = total_reversals / simulation_count
    
    print(f"\n分析结果:")
    print(f"总反转点数: {total_reversals}")
    print(f"平均每次模拟反转次数: {avg_reversals:.2f}")
    
    # 显示价格图表
    plt.figure(figsize=(15, 10))
    
    for i, result in enumerate(results):
        plt.subplot(2, 2, i+1)
        plt.plot(result['price_history'], label='价格', alpha=0.7)
        plt.plot(result['value_history'], label='真实价值', alpha=0.7)
        
        # 标记反转点
        for day, rev_type, price, magnitude in result['reversals']:
            color = 'red' if rev_type == 'peak' else 'green'
            plt.scatter(day, price, color=color, s=50, alpha=0.8)
        
        plt.title(f'模拟 {i+1} (种子: {result["seed"]})')
        plt.xlabel('交易日')
        plt.ylabel('价格')
        plt.legend()
        plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
