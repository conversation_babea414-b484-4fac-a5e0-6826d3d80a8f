#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
趋势反转规律分析程序
通过多次模拟运行，分析趋势反转的一般规律和原因

作者: AI Assistant
版本: 1.0
日期: 2024
"""

import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from collections import defaultdict, Counter
import json
import os
from datetime import datetime
import random

# 导入原有的模拟框架
import sys
sys.path.append('.')

# 由于文件名包含点号，需要使用importlib导入
import importlib.util
spec = importlib.util.spec_from_file_location("main_module", "main_OHLC_2.0.6.1.py")
main_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(main_module)

# 从模块中导入需要的类和函数
TrueValueCurve = main_module.TrueValueCurve
ValueInvestor = main_module.ValueInvestor
ChaseInvestor = main_module.ChaseInvestor
TrendInvestor = main_module.TrendInvestor
RandomInvestor = main_module.RandomInvestor
NeverStopLossInvestor = main_module.NeverStopLossInvestor
BottomFishingInvestor = main_module.BottomFishingInvestor
InsiderInvestor = main_module.InsiderInvestor
MessageInvestor = main_module.MessageInvestor
Market = main_module.Market
analyze_trend_reversal_intrinsic_factors = main_module.analyze_trend_reversal_intrinsic_factors

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class TrendReversalPatternAnalyzer:
    """
    趋势反转模式分析器
    通过多次模拟分析反转的一般规律
    """
    
    def __init__(self, simulation_days=1000, num_simulations=50):
        self.simulation_days = simulation_days
        self.num_simulations = num_simulations
        self.simulation_results = []
        self.aggregated_patterns = {
            'reversal_frequency': [],
            'reversal_types': [],
            'reversal_magnitudes': [],
            'key_factors': [],
            'investor_behaviors': defaultdict(list),
            'market_conditions': [],
            'timing_patterns': []
        }
    
    def run_single_simulation(self, seed):
        """
        运行单次模拟
        """
        print(f"正在运行第 {len(self.simulation_results) + 1} 次模拟 (种子: {seed})...")
        
        try:
        
        # 设置随机种子
        np.random.seed(seed)
        random.seed(seed)
        
        # 创建真实价值曲线
        true_value_curve = TrueValueCurve(seed=seed)
        
        # 创建市场
        market = Market()
        
        # 创建投资者
        investors = []
        num_investors_per_type = 100
        
        # Value投资者
        for i in range(num_investors_per_type):
            investors.append(ValueInvestor(f"Value_{i}", 10000, 100))
        
        # Chase投资者
        for i in range(num_investors_per_type):
            investors.append(ChaseInvestor(f"Chase_{i}", 10000, 100))
        
        # Trend投资者
        for i in range(num_investors_per_type):
            investors.append(TrendInvestor(f"Trend_{i}", 10000, 100))
        
        # Random投资者
        for i in range(num_investors_per_type):
            investors.append(RandomInvestor(f"Random_{i}", 10000, 100))
        
        # NeverStopLoss投资者
        for i in range(num_investors_per_type):
            investors.append(NeverStopLossInvestor(f"NeverStopLoss_{i}", 10000, 100))
        
        # BottomFishing投资者
        for i in range(num_investors_per_type):
            investors.append(BottomFishingInvestor(f"BottomFishing_{i}", 10000, 100))
        
        # Insider投资者
        for i in range(num_investors_per_type):
            investors.append(InsiderInvestor(f"Insider_{i}", 10000, 100))
        
        # Message投资者
        for i in range(num_investors_per_type):
            investors.append(MessageInvestor(f"Message_{i}", 10000, 100))
        
        # 按类型分组统计数据
        investor_types = ['Value', 'Chase', 'Trend', 'Random', 'NeverStopLoss', 
                         'BottomFishing', 'Insider', 'Message']
        shares_by_type = {t: [] for t in investor_types}
        cash_by_type = {t: [] for t in investor_types}
        wealth_by_type = {t: [] for t in investor_types}
        
        # 模拟交易
        for day in range(self.simulation_days):
            # 获取当天真实价值
            true_value = true_value_curve.get_value(day)
            
            # 更新市场情绪
            market.update_market_sentiment()
            
            # 投资者决策和交易
            for investor in investors:
                if hasattr(investor, 'update_value_estimate'):
                    investor.update_value_estimate(true_value, market.sentiment_history[-1] if market.sentiment_history else 0)
                
                price = investor.decide_price(market.price_history[-1] if market.price_history else 100, 
                                            market.sentiment_history[-1] if market.sentiment_history else 0)
                volume = investor.trade(market.price_history[-1] if market.price_history else 100, 
                                      market.sentiment_history[-1] if market.sentiment_history else 0)
                
                if volume > 0:
                    market.place_order('buy', price, volume, investor.name)
                elif volume < 0:
                    market.place_order('sell', price, -volume, investor.name)
            
            # 市场撮合
            market.call_auction()
            
            # 记录统计数据
            for investor_type in investor_types:
                type_investors = [inv for inv in investors if inv.name.startswith(investor_type)]
                avg_shares = np.mean([inv.shares for inv in type_investors])
                avg_cash = np.mean([inv.cash for inv in type_investors])
                current_price = market.price_history[-1] if market.price_history else 100
                avg_wealth = np.mean([inv.cash + inv.shares * current_price for inv in type_investors])
                
                shares_by_type[investor_type].append(avg_shares)
                cash_by_type[investor_type].append(avg_cash)
                wealth_by_type[investor_type].append(avg_wealth)
        
        # 分析趋势反转
        analysis_report = analyze_trend_reversal_intrinsic_factors(
            market, investors, shares_by_type, cash_by_type, wealth_by_type
        )
        
        # 保存单次模拟结果
        simulation_result = {
            'seed': seed,
            'market_data': {
                'price_history': market.price_history,
                'volume_history': market.executed_volume_history,
                'sentiment_history': market.sentiment_history
            },
            'analysis_report': analysis_report,
            'final_price': market.price_history[-1] if market.price_history else 100,
            'total_return': (market.price_history[-1] / market.price_history[0] - 1) * 100 if market.price_history else 0
        }
        
            self.simulation_results.append(simulation_result)
            return simulation_result
        
        except Exception as e:
            print(f"模拟失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def run_multiple_simulations(self, start_seed=1000):
        """
        运行多次模拟
        """
        print(f"开始运行 {self.num_simulations} 次模拟...")
        
        for i in range(self.num_simulations):
            seed = start_seed + i
            result = self.run_single_simulation(seed)
            if result is None:
                print(f"模拟 {i+1} (种子: {seed}) 失败")
                continue
        
        print(f"完成 {len(self.simulation_results)} 次有效模拟")
    
    def analyze_reversal_patterns(self):
        """
        分析反转模式
        """
        print("正在分析反转模式...")
        
        all_reversals = []
        reversal_frequencies = []
        reversal_magnitudes = []
        key_factors_counter = Counter()
        investor_behavior_patterns = defaultdict(list)
        
        for result in self.simulation_results:
            analysis = result['analysis_report']
            reversals = analysis['trend_reversals']
            
            # 反转频率
            reversal_frequencies.append(len(reversals))
            
            # 分析每个反转点
            for reversal in reversals:
                all_reversals.append(reversal)
                
                # 反转幅度
                if 'price' in reversal:
                    price_history = result['market_data']['price_history']
                    if reversal['day'] < len(price_history):
                        reversal_magnitudes.append(abs(reversal['price'] - 100) / 100)
                
                # 关键因素统计
                for factor in reversal.get('key_factors', []):
                    key_factors_counter[factor] += 1
                
                # 投资者行为模式
                for investor_type, changes in reversal.get('capital_flow_changes', {}).items():
                    cash_change = changes.get('cash_change', 0)
                    shares_change = changes.get('shares_change', 0)
                    
                    investor_behavior_patterns[investor_type].append({
                        'cash_change': cash_change,
                        'shares_change': shares_change,
                        'reversal_type': reversal['type']
                    })
        
        # 汇总分析结果
        if not reversal_frequencies:
            print("警告: 没有有效的反转数据")
            reversal_frequencies = [0]
        
        self.aggregated_patterns = {
            'reversal_frequency': {
                'mean': np.mean(reversal_frequencies),
                'std': np.std(reversal_frequencies),
                'min': np.min(reversal_frequencies),
                'max': np.max(reversal_frequencies),
                'distribution': reversal_frequencies
            },
            'reversal_types': {
                'peaks': len([r for r in all_reversals if r['type'] == 'peak']),
                'troughs': len([r for r in all_reversals if r['type'] == 'trough']),
                'total': len(all_reversals)
            },
            'reversal_magnitudes': {
                'mean': np.mean(reversal_magnitudes) if reversal_magnitudes else 0,
                'std': np.std(reversal_magnitudes) if reversal_magnitudes else 0,
                'distribution': reversal_magnitudes if reversal_magnitudes else [0]
            },
            'key_factors': dict(key_factors_counter.most_common()),
            'investor_behaviors': dict(investor_behavior_patterns),
            'market_conditions': {
                'avg_final_return': np.mean([r['total_return'] for r in self.simulation_results]),
                'return_volatility': np.std([r['total_return'] for r in self.simulation_results])
            }
        }
        
        return self.aggregated_patterns
    
    def generate_pattern_report(self):
        """
        生成模式分析报告
        """
        patterns = self.aggregated_patterns
        
        report = f"""
================================================================================
趋势反转规律分析报告
================================================================================

一、模拟概况
----------------------------------------
模拟次数: {len(self.simulation_results)}次
每次模拟天数: {self.simulation_days}天
总分析数据点: {len(self.simulation_results) * self.simulation_days}个

二、反转频率分析
----------------------------------------
平均反转次数: {patterns['reversal_frequency']['mean']:.2f}次/模拟
反转频率标准差: {patterns['reversal_frequency']['std']:.2f}
最少反转次数: {patterns['reversal_frequency']['min']}次
最多反转次数: {patterns['reversal_frequency']['max']}次
反转频率分布: {patterns['reversal_frequency']['distribution']}

三、反转类型分析
----------------------------------------
总反转点数: {patterns['reversal_types']['total']}个
峰值反转: {patterns['reversal_types']['peaks']}个 ({patterns['reversal_types']['peaks']/max(patterns['reversal_types']['total'], 1)*100:.1f}%)
谷值反转: {patterns['reversal_types']['troughs']}个 ({patterns['reversal_types']['troughs']/max(patterns['reversal_types']['total'], 1)*100:.1f}%)

四、反转幅度分析
----------------------------------------
平均反转幅度: {patterns['reversal_magnitudes']['mean']*100:.2f}%
反转幅度标准差: {patterns['reversal_magnitudes']['std']*100:.2f}%

五、关键因素分析
----------------------------------------
最常见的反转因素:
"""
        
        # 添加关键因素排名
        for i, (factor, count) in enumerate(list(patterns['key_factors'].items())[:10]):
            percentage = count / max(patterns['reversal_types']['total'], 1) * 100
            report += f"  {i+1}. {factor}: {count}次 ({percentage:.1f}%)\n"
        
        report += "\n六、投资者行为模式分析\n" + "-" * 40 + "\n"
        
        # 分析每种投资者类型的行为模式
        for investor_type, behaviors in patterns['investor_behaviors'].items():
            if behaviors:
                cash_changes = [b['cash_change'] for b in behaviors]
                shares_changes = [b['shares_change'] for b in behaviors]
                
                avg_cash_change = np.mean(cash_changes) * 100
                avg_shares_change = np.mean(shares_changes) * 100
                
                # 判断主要行为倾向
                buy_tendency = len([b for b in behaviors if b['shares_change'] > 0])
                sell_tendency = len([b for b in behaviors if b['shares_change'] < 0])
                
                report += f"{investor_type}投资者:\n"
                report += f"  平均现金变化: {avg_cash_change:+.2f}%\n"
                report += f"  平均持仓变化: {avg_shares_change:+.2f}%\n"
                report += f"  买入倾向: {buy_tendency}次, 卖出倾向: {sell_tendency}次\n"
                
                if buy_tendency > sell_tendency:
                    report += f"  → 在反转点主要表现为买入行为\n"
                elif sell_tendency > buy_tendency:
                    report += f"  → 在反转点主要表现为卖出行为\n"
                else:
                    report += f"  → 在反转点买卖行为均衡\n"
                report += "\n"
        
        report += f"""
七、市场整体表现
----------------------------------------
平均收益率: {patterns['market_conditions']['avg_final_return']:.2f}%
收益率波动性: {patterns['market_conditions']['return_volatility']:.2f}%

八、反转规律总结
----------------------------------------
"""
        
        # 生成规律总结
        freq_mean = patterns['reversal_frequency']['mean']
        if freq_mean > 10:
            report += "1. 市场反转频繁，平均每次模拟出现超过10次反转\n"
        elif freq_mean > 5:
            report += "1. 市场反转适中，平均每次模拟出现5-10次反转\n"
        else:
            report += "1. 市场反转较少，平均每次模拟出现少于5次反转\n"
        
        peak_ratio = patterns['reversal_types']['peaks'] / max(patterns['reversal_types']['total'], 1)
        if peak_ratio > 0.6:
            report += "2. 峰值反转占主导地位，市场更容易在高点反转\n"
        elif peak_ratio < 0.4:
            report += "2. 谷值反转占主导地位，市场更容易在低点反转\n"
        else:
            report += "2. 峰值和谷值反转相对均衡\n"
        
        # 分析主要推动因素
        if patterns['key_factors']:
            top_factor = list(patterns['key_factors'].keys())[0]
            report += f"3. 最主要的反转推动因素: {top_factor}\n"
        
        # 分析投资者行为特征
        value_behaviors = patterns['investor_behaviors'].get('Value', [])
        if value_behaviors:
            value_sells = len([b for b in value_behaviors if b['shares_change'] < 0])
            value_total = len(value_behaviors)
            if value_sells / value_total > 0.7:
                report += "4. Value投资者在反转点主要表现为卖出，可能起到反转信号作用\n"
        
        chase_behaviors = patterns['investor_behaviors'].get('Chase', [])
        if chase_behaviors:
            chase_buys = len([b for b in chase_behaviors if b['shares_change'] > 0])
            chase_total = len(chase_behaviors)
            if chase_buys / chase_total > 0.7:
                report += "5. Chase投资者在反转点主要表现为买入，可能加剧市场波动\n"
        
        report += "\n" + "=" * 80 + "\n"
        
        return report
    
    def create_visualization(self):
        """
        创建可视化图表
        """
        patterns = self.aggregated_patterns
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('趋势反转规律分析可视化', fontsize=16, fontweight='bold')
        
        # 1. 反转频率分布
        axes[0, 0].hist(patterns['reversal_frequency']['distribution'], bins=10, alpha=0.7, color='skyblue')
        axes[0, 0].set_title('反转频率分布')
        axes[0, 0].set_xlabel('每次模拟的反转次数')
        axes[0, 0].set_ylabel('频次')
        axes[0, 0].axvline(patterns['reversal_frequency']['mean'], color='red', linestyle='--', 
                          label=f'平均值: {patterns["reversal_frequency"]["mean"]:.1f}')
        axes[0, 0].legend()
        
        # 2. 反转类型比例
        if patterns['reversal_types']['total'] > 0:
            labels = ['峰值反转', '谷值反转']
            sizes = [patterns['reversal_types']['peaks'], patterns['reversal_types']['troughs']]
            axes[0, 1].pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90)
            axes[0, 1].set_title('反转类型分布')
        
        # 3. 反转幅度分布
        if patterns['reversal_magnitudes']['distribution']:
            axes[0, 2].hist([m*100 for m in patterns['reversal_magnitudes']['distribution']], 
                           bins=15, alpha=0.7, color='lightgreen')
            axes[0, 2].set_title('反转幅度分布')
            axes[0, 2].set_xlabel('反转幅度 (%)')
            axes[0, 2].set_ylabel('频次')
        
        # 4. 关键因素排名
        if patterns['key_factors']:
            factors = list(patterns['key_factors'].keys())[:8]
            counts = list(patterns['key_factors'].values())[:8]
            axes[1, 0].barh(range(len(factors)), counts, color='orange', alpha=0.7)
            axes[1, 0].set_yticks(range(len(factors)))
            axes[1, 0].set_yticklabels([f[:20] + '...' if len(f) > 20 else f for f in factors])
            axes[1, 0].set_title('主要反转因素')
            axes[1, 0].set_xlabel('出现次数')
        
        # 5. 投资者行为热力图
        investor_types = list(patterns['investor_behaviors'].keys())
        if investor_types:
            behavior_matrix = []
            for inv_type in investor_types:
                behaviors = patterns['investor_behaviors'][inv_type]
                if behaviors:
                    avg_cash = np.mean([b['cash_change'] for b in behaviors])
                    avg_shares = np.mean([b['shares_change'] for b in behaviors])
                    behavior_matrix.append([avg_cash, avg_shares])
                else:
                    behavior_matrix.append([0, 0])
            
            if behavior_matrix:
                im = axes[1, 1].imshow(behavior_matrix, cmap='RdYlBu', aspect='auto')
                axes[1, 1].set_xticks([0, 1])
                axes[1, 1].set_xticklabels(['现金变化', '持仓变化'])
                axes[1, 1].set_yticks(range(len(investor_types)))
                axes[1, 1].set_yticklabels(investor_types)
                axes[1, 1].set_title('投资者行为模式')
                plt.colorbar(im, ax=axes[1, 1])
        
        # 6. 收益率分布
        returns = [r['total_return'] for r in self.simulation_results]
        axes[1, 2].hist(returns, bins=15, alpha=0.7, color='purple')
        axes[1, 2].set_title('模拟收益率分布')
        axes[1, 2].set_xlabel('收益率 (%)')
        axes[1, 2].set_ylabel('频次')
        axes[1, 2].axvline(np.mean(returns), color='red', linestyle='--', 
                          label=f'平均值: {np.mean(returns):.1f}%')
        axes[1, 2].legend()
        
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        chart_filename = f"trend_reversal_patterns_{timestamp}.png"
        plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
        print(f"可视化图表已保存到: {chart_filename}")
        
        return chart_filename
    
    def save_results(self):
        """
        保存分析结果
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存详细报告
        report = self.generate_pattern_report()
        report_filename = f"trend_reversal_patterns_report_{timestamp}.txt"
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(report)
        
        # 保存原始数据
        data_filename = f"trend_reversal_raw_data_{timestamp}.json"
        with open(data_filename, 'w', encoding='utf-8') as f:
            # 简化数据以避免JSON序列化问题
            simplified_results = []
            for result in self.simulation_results:
                simplified_result = {
                    'seed': result['seed'],
                    'final_price': result['final_price'],
                    'total_return': result['total_return'],
                    'reversal_count': len(result['analysis_report']['trend_reversals']),
                    'reversal_types': [r['type'] for r in result['analysis_report']['trend_reversals']]
                }
                simplified_results.append(simplified_result)
            
            json.dump({
                'simulation_config': {
                    'num_simulations': self.num_simulations,
                    'simulation_days': self.simulation_days
                },
                'aggregated_patterns': self.aggregated_patterns,
                'simplified_results': simplified_results
            }, f, ensure_ascii=False, indent=2)
        
        print(f"分析报告已保存到: {report_filename}")
        print(f"原始数据已保存到: {data_filename}")
        
        return report_filename, data_filename

def main():
    """
    主函数
    """
    print("=" * 60)
    print("趋势反转规律分析程序")
    print("=" * 60)
    
    # 获取用户输入
    try:
        num_simulations = int(input("请输入模拟次数 (建议10-100次): ") or "20")
        simulation_days = int(input("请输入每次模拟天数 (建议500-2000天): ") or "1000")
    except ValueError:
        print("输入无效，使用默认值")
        num_simulations = 20
        simulation_days = 1000
    
    print(f"\n配置: {num_simulations}次模拟，每次{simulation_days}天")
    print("开始分析...\n")
    
    # 创建分析器
    analyzer = TrendReversalPatternAnalyzer(
        simulation_days=simulation_days,
        num_simulations=num_simulations
    )
    
    # 运行模拟
    analyzer.run_multiple_simulations()
    
    # 分析模式
    patterns = analyzer.analyze_reversal_patterns()
    
    # 生成报告
    report = analyzer.generate_pattern_report()
    print("\n" + "=" * 60)
    print("分析完成！")
    print("=" * 60)
    print(report)
    
    # 保存结果
    report_file, data_file = analyzer.save_results()
    
    # 创建可视化
    chart_file = analyzer.create_visualization()
    
    print(f"\n所有结果文件:")
    print(f"- 分析报告: {report_file}")
    print(f"- 原始数据: {data_file}")
    print(f"- 可视化图表: {chart_file}")

if __name__ == "__main__":
    main()