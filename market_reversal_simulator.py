#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股市趋势反转深度分析模拟器
基于main_OHLC_2.0.6.1.py进行多次模拟，深度分析价格趋势反转的内在原因

功能特点:
1. 多次模拟运行，使用不同随机数种子
2. 深度跟踪市场参与者的资金面、持仓、交易情况
3. 识别价格趋势反转点（顶部和底部）
4. 分析导致反转的内在因素（非技术指标）
5. 生成详细的分析报告和规律总结

作者: AI Assistant
版本: 1.0
日期: 2025-07-05
"""

import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from collections import defaultdict, Counter
import json
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入主模拟程序
import sys
import importlib.util

# 动态导入main_OHLC_2.0.6.1.py
spec = importlib.util.spec_from_file_location("main_OHLC", "main_OHLC_2.0.6.1.py")
main_module = importlib.util.module_from_spec(spec)
sys.modules["main_OHLC"] = main_module
spec.loader.exec_module(main_module)

# 导入需要的类
TrueValueCurve = main_module.TrueValueCurve
Investor = main_module.Investor
ValueInvestor = main_module.ValueInvestor
ChaseInvestor = main_module.ChaseInvestor
TrendInvestor = main_module.TrendInvestor
RandomInvestor = main_module.RandomInvestor
NeverStopLossInvestor = main_module.NeverStopLossInvestor
BottomFishingInvestor = main_module.BottomFishingInvestor
InsiderInvestor = main_module.InsiderInvestor
MessageInvestor = main_module.MessageInvestor
Market = main_module.Market

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class MarketReversalSimulator:
    """
    市场反转模拟器
    """
    
    def __init__(self, simulation_count=50):
        """
        初始化模拟器
        
        Args:
            simulation_count: 模拟次数
        """
        self.simulation_count = simulation_count
        self.simulation_results = []
        self.reversal_data = []
        self.market_states = []
        
    def detect_reversals(self, prices, window=10, min_change=0.05):
        """
        检测价格趋势反转点
        
        Args:
            prices: 价格序列
            window: 检测窗口大小
            min_change: 最小变化幅度
            
        Returns:
            list: 反转点信息 [(day, type, price, magnitude), ...]
        """
        reversals = []
        
        for i in range(window, len(prices) - window):
            current_price = prices[i]
            
            # 检查是否为局部最高点（峰值）
            is_peak = True
            for j in range(i - window, i + window + 1):
                if j != i and prices[j] >= current_price:
                    is_peak = False
                    break
            
            # 检查是否为局部最低点（谷值）
            is_valley = True
            for j in range(i - window, i + window + 1):
                if j != i and prices[j] <= current_price:
                    is_valley = False
                    break
            
            if is_peak or is_valley:
                # 计算反转幅度
                prev_extreme = None
                for k in range(len(reversals) - 1, -1, -1):
                    if reversals[k][1] != ('peak' if is_peak else 'valley'):
                        prev_extreme = reversals[k][2]
                        break
                
                if prev_extreme is not None:
                    magnitude = abs(current_price - prev_extreme) / prev_extreme
                    if magnitude >= min_change:
                        reversal_type = 'peak' if is_peak else 'valley'
                        reversals.append((i, reversal_type, current_price, magnitude))
                else:
                    # 第一个反转点
                    reversal_type = 'peak' if is_peak else 'valley'
                    reversals.append((i, reversal_type, current_price, 0.0))
        
        return reversals
    
    def analyze_market_state(self, market, day):
        """
        分析市场状态
        
        Args:
            market: 市场对象
            day: 当前天数
            
        Returns:
            dict: 市场状态信息
        """
        state = {
            'day': day,
            'price': market.price_history[day] if day < len(market.price_history) else 0,
            'volume': market.executed_volume_history[day] if day < len(market.executed_volume_history) else 0,
            'true_value': market.value_curve[day] if day < len(market.value_curve) else 0,
            'investor_states': {}
        }
        
        # 分析各类投资者状态
        investor_types = ['ValueInvestor', 'ChaseInvestor', 'TrendInvestor', 'RandomInvestor', 
                         'NeverStopLossInvestor', 'BottomFishingInvestor', 'InsiderInvestor', 'MessageInvestor']
        
        for inv_type in investor_types:
            investors = [inv for inv in market.investors if inv.__class__.__name__ == inv_type]
            if investors:
                total_cash = sum(inv.cash for inv in investors)
                total_shares = sum(inv.shares for inv in investors)
                total_assets = total_cash + total_shares * state['price']
                avg_position_ratio = (total_shares * state['price']) / total_assets if total_assets > 0 else 0
                
                state['investor_states'][inv_type] = {
                    'count': len(investors),
                    'total_cash': total_cash,
                    'total_shares': total_shares,
                    'total_assets': total_assets,
                    'avg_position_ratio': avg_position_ratio,
                    'cash_ratio': total_cash / total_assets if total_assets > 0 else 0
                }
        
        return state
    
    def run_single_simulation(self, seed):
        """
        运行单次模拟

        Args:
            seed: 随机数种子

        Returns:
            dict: 模拟结果
        """
        print(f"运行模拟 {seed}...")

        # 设置随机种子
        np.random.seed(seed)

        # 基本市场参数设置（优化性能）
        initial_price = 100
        price_tick = 0.01
        days = 500  # 减少天数以提高性能
        buy_fee_rate = 0.001
        sell_fee_rate = 0.001

        # 禁用交易日志以提高性能
        Investor.set_enable_trade_log(False)

        # 创建价值曲线
        value_curve = TrueValueCurve(initial_value=initial_price, days=days, seed=seed)

        # 创建市场实例
        market = Market(initial_price, price_tick, value_curve=value_curve, seed=seed,
                       buy_fee_rate=buy_fee_rate, sell_fee_rate=sell_fee_rate)

        # 创建投资者
        investors = []

        # 创建价值投资者（减少数量以提高性能）
        for i in range(30):
            bias_percent = np.random.normal(0, 0.15)
            max_deviation = np.random.uniform(0.2, 0.4)
            target_position = np.random.uniform(0.4, 0.6)
            investors.append(ValueInvestor(100, 10000, bias_percent=bias_percent,
                                         max_deviation=max_deviation, target_position=target_position,
                                         seed=seed + i))

        # 创建追涨杀跌投资者
        for i in range(30):
            investors.append(ChaseInvestor(100, 10000))

        # 创建趋势投资者
        trend_periods = [5, 10, 15, 20]
        for period in trend_periods:
            for i in range(5):
                investors.append(TrendInvestor(100, 10000, period))

        # 创建随机投资者
        for i in range(20):
            investors.append(RandomInvestor(100, 10000, seed=seed + i + 1000))

        # 创建其他类型投资者
        for i in range(10):
            investors.append(NeverStopLossInvestor(0, 20000, seed=seed + i + 2000))

        for i in range(10):
            investors.append(BottomFishingInvestor(100, 10000, seed=seed + i + 3000))

        # 将投资者添加到市场
        market.investors = investors

        # 记录市场状态
        market_states = []

        # 运行模拟
        for day in range(days):
            # 投资者决策和下单
            for investor in investors:
                investor.trade(market.price, market)

            # 执行交易
            market.daily_auction()

            # 每10天记录一次市场状态
            if day % 10 == 0:
                state = self.analyze_market_state(market, day)
                market_states.append(state)

        # 检测反转点
        reversals = self.detect_reversals(market.price_history)

        # 计算最终收益
        final_price = market.price_history[-1]
        initial_price = market.price_history[0]
        total_return = (final_price - initial_price) / initial_price

        result = {
            'seed': seed,
            'price_history': market.price_history,
            'volume_history': market.executed_volume_history,
            'value_history': market.value_curve.values,
            'reversals': reversals,
            'market_states': market_states,
            'total_return': total_return,
            'final_price': final_price,
            'max_price': max(market.price_history),
            'min_price': min(market.price_history),
            'volatility': np.std(market.price_history) / np.mean(market.price_history)
        }

        return result
    
    def run_simulations(self):
        """
        运行所有模拟
        """
        print(f"开始运行 {self.simulation_count} 次模拟...")
        
        for i in range(self.simulation_count):
            seed = i + 1000  # 使用不同的种子
            result = self.run_single_simulation(seed)
            self.simulation_results.append(result)
            
            # 收集反转数据
            for reversal in result['reversals']:
                day, rev_type, price, magnitude = reversal
                self.reversal_data.append({
                    'simulation': i,
                    'day': day,
                    'type': rev_type,
                    'price': price,
                    'magnitude': magnitude,
                    'market_state': self.get_market_state_at_day(result['market_states'], day)
                })
        
        print(f"完成 {self.simulation_count} 次模拟")
    
    def get_market_state_at_day(self, market_states, day):
        """
        获取指定天数的市场状态
        """
        # 找到最接近的记录天数
        closest_state = None
        min_diff = float('inf')
        
        for state in market_states:
            diff = abs(state['day'] - day)
            if diff < min_diff:
                min_diff = diff
                closest_state = state
        
        return closest_state

    def analyze_reversal_causes(self):
        """
        分析反转原因

        Returns:
            dict: 反转原因分析结果
        """
        causes = defaultdict(int)
        reversal_patterns = []

        for reversal in self.reversal_data:
            if reversal['market_state'] is None:
                continue

            state = reversal['market_state']
            rev_type = reversal['type']
            magnitude = reversal['magnitude']

            # 分析资金面因素
            total_cash_ratio = 0
            total_position_ratio = 0
            investor_count = 0

            for inv_type, inv_state in state['investor_states'].items():
                if inv_state['total_assets'] > 0:
                    total_cash_ratio += inv_state['cash_ratio'] * inv_state['total_assets']
                    total_position_ratio += inv_state['avg_position_ratio'] * inv_state['total_assets']
                    investor_count += inv_state['count']

            total_assets = sum(inv_state['total_assets'] for inv_state in state['investor_states'].values())
            if total_assets > 0:
                avg_cash_ratio = total_cash_ratio / total_assets
                avg_position_ratio = total_position_ratio / total_assets
            else:
                avg_cash_ratio = 0
                avg_position_ratio = 0

            # 分析价值偏离
            price_value_ratio = state['price'] / state['true_value'] if state['true_value'] > 0 else 1

            # 分析交易量变化
            volume_change = 0
            if len(self.simulation_results) > 0:
                sim_result = self.simulation_results[reversal['simulation']]
                if reversal['day'] > 10:
                    recent_volume = np.mean(sim_result['volume_history'][max(0, reversal['day']-10):reversal['day']])
                    current_volume = state['volume']
                    if recent_volume > 0:
                        volume_change = (current_volume - recent_volume) / recent_volume

            # 识别反转原因
            pattern = {
                'type': rev_type,
                'magnitude': magnitude,
                'cash_ratio': avg_cash_ratio,
                'position_ratio': avg_position_ratio,
                'price_value_ratio': price_value_ratio,
                'volume_change': volume_change,
                'day': reversal['day']
            }

            reversal_patterns.append(pattern)

            # 分类反转原因
            if rev_type == 'peak':  # 顶部反转
                if avg_position_ratio > 0.8:
                    causes['高仓位导致顶部反转'] += 1
                if price_value_ratio > 1.2:
                    causes['价格严重高估导致顶部反转'] += 1
                if avg_cash_ratio < 0.2:
                    causes['资金不足导致顶部反转'] += 1
                if volume_change < -0.3:
                    causes['交易量萎缩导致顶部反转'] += 1
            else:  # 底部反转
                if avg_cash_ratio > 0.6:
                    causes['充足资金导致底部反转'] += 1
                if price_value_ratio < 0.8:
                    causes['价格严重低估导致底部反转'] += 1
                if avg_position_ratio < 0.3:
                    causes['低仓位导致底部反转'] += 1
                if volume_change > 0.5:
                    causes['交易量放大导致底部反转'] += 1

        return {
            'causes': dict(causes),
            'patterns': reversal_patterns
        }

    def generate_detailed_report(self):
        """
        生成详细分析报告
        """
        if not self.simulation_results:
            print("请先运行模拟")
            return

        # 分析反转原因
        reversal_analysis = self.analyze_reversal_causes()

        # 统计数据
        total_reversals = len(self.reversal_data)
        peak_reversals = len([r for r in self.reversal_data if r['type'] == 'peak'])
        valley_reversals = len([r for r in self.reversal_data if r['type'] == 'valley'])

        avg_reversals_per_sim = total_reversals / self.simulation_count
        avg_magnitude = np.mean([r['magnitude'] for r in self.reversal_data if r['magnitude'] > 0])

        returns = [r['total_return'] for r in self.simulation_results]
        avg_return = np.mean(returns)
        return_std = np.std(returns)

        # 生成报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"market_reversal_analysis_report_{timestamp}.txt"

        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("股市趋势反转深度分析报告\n")
            f.write("=" * 80 + "\n\n")

            f.write("一、模拟概况\n")
            f.write("-" * 40 + "\n")
            f.write(f"模拟次数: {self.simulation_count}次\n")
            f.write(f"总反转点数: {total_reversals}个\n")
            f.write(f"平均每次模拟反转次数: {avg_reversals_per_sim:.2f}次\n\n")

            f.write("二、反转类型分析\n")
            f.write("-" * 40 + "\n")
            f.write(f"峰值反转（顶部）: {peak_reversals}个 ({peak_reversals/total_reversals*100:.1f}%)\n")
            f.write(f"谷值反转（底部）: {valley_reversals}个 ({valley_reversals/total_reversals*100:.1f}%)\n")
            f.write(f"平均反转幅度: {avg_magnitude*100:.2f}%\n\n")

            f.write("三、市场表现\n")
            f.write("-" * 40 + "\n")
            f.write(f"平均收益率: {avg_return*100:.2f}%\n")
            f.write(f"收益率标准差: {return_std*100:.2f}%\n")
            f.write(f"最佳表现: {max(returns)*100:.2f}%\n")
            f.write(f"最差表现: {min(returns)*100:.2f}%\n\n")

            f.write("四、反转内在原因分析\n")
            f.write("-" * 40 + "\n")
            causes = reversal_analysis['causes']
            sorted_causes = sorted(causes.items(), key=lambda x: x[1], reverse=True)

            for i, (cause, count) in enumerate(sorted_causes[:10], 1):
                percentage = count / total_reversals * 100
                f.write(f"{i:2d}. {cause}: {count}次 ({percentage:.1f}%)\n")

            f.write("\n五、反转规律总结\n")
            f.write("-" * 40 + "\n")

            # 分析顶部反转规律
            peak_patterns = [p for p in reversal_analysis['patterns'] if p['type'] == 'peak']
            if peak_patterns:
                avg_peak_cash = np.mean([p['cash_ratio'] for p in peak_patterns])
                avg_peak_position = np.mean([p['position_ratio'] for p in peak_patterns])
                avg_peak_value_ratio = np.mean([p['price_value_ratio'] for p in peak_patterns])

                f.write("顶部反转规律:\n")
                f.write(f"  - 平均现金比例: {avg_peak_cash*100:.1f}%\n")
                f.write(f"  - 平均仓位比例: {avg_peak_position*100:.1f}%\n")
                f.write(f"  - 平均价格/价值比: {avg_peak_value_ratio:.2f}\n")

            # 分析底部反转规律
            valley_patterns = [p for p in reversal_analysis['patterns'] if p['type'] == 'valley']
            if valley_patterns:
                avg_valley_cash = np.mean([p['cash_ratio'] for p in valley_patterns])
                avg_valley_position = np.mean([p['position_ratio'] for p in valley_patterns])
                avg_valley_value_ratio = np.mean([p['price_value_ratio'] for p in valley_patterns])

                f.write("\n底部反转规律:\n")
                f.write(f"  - 平均现金比例: {avg_valley_cash*100:.1f}%\n")
                f.write(f"  - 平均仓位比例: {avg_valley_position*100:.1f}%\n")
                f.write(f"  - 平均价格/价值比: {avg_valley_value_ratio:.2f}\n")

            f.write("\n六、关键发现\n")
            f.write("-" * 40 + "\n")
            f.write("1. 资金面是影响反转的关键因素\n")
            f.write("2. 价格与内在价值的偏离程度决定反转时机\n")
            f.write("3. 投资者仓位结构变化预示反转可能\n")
            f.write("4. 交易量变化是反转的重要信号\n")

            f.write("\n" + "=" * 80 + "\n")

        print(f"详细分析报告已保存至: {report_filename}")
        return report_filename

    def create_visualizations(self):
        """
        创建可视化图表
        """
        if not self.simulation_results:
            print("请先运行模拟")
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 创建图表
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('股市趋势反转深度分析', fontsize=16, fontweight='bold')

        # 1. 反转频率分布
        reversal_counts = [len(r['reversals']) for r in self.simulation_results]
        axes[0, 0].hist(reversal_counts, bins=15, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].set_title('反转频率分布')
        axes[0, 0].set_xlabel('每次模拟的反转次数')
        axes[0, 0].set_ylabel('频次')
        axes[0, 0].grid(True, alpha=0.3)

        # 2. 反转幅度分布
        magnitudes = [r['magnitude'] for r in self.reversal_data if r['magnitude'] > 0]
        axes[0, 1].hist(magnitudes, bins=20, alpha=0.7, color='lightcoral', edgecolor='black')
        axes[0, 1].set_title('反转幅度分布')
        axes[0, 1].set_xlabel('反转幅度')
        axes[0, 1].set_ylabel('频次')
        axes[0, 1].grid(True, alpha=0.3)

        # 3. 反转类型比例
        peak_count = len([r for r in self.reversal_data if r['type'] == 'peak'])
        valley_count = len([r for r in self.reversal_data if r['type'] == 'valley'])
        axes[0, 2].pie([peak_count, valley_count], labels=['顶部反转', '底部反转'],
                       autopct='%1.1f%%', colors=['lightcoral', 'lightgreen'])
        axes[0, 2].set_title('反转类型分布')

        # 4. 收益率分布
        returns = [r['total_return'] * 100 for r in self.simulation_results]
        axes[1, 0].hist(returns, bins=15, alpha=0.7, color='gold', edgecolor='black')
        axes[1, 0].set_title('收益率分布')
        axes[1, 0].set_xlabel('收益率 (%)')
        axes[1, 0].set_ylabel('频次')
        axes[1, 0].grid(True, alpha=0.3)

        # 5. 反转时间分布
        reversal_days = [r['day'] for r in self.reversal_data]
        axes[1, 1].hist(reversal_days, bins=20, alpha=0.7, color='mediumpurple', edgecolor='black')
        axes[1, 1].set_title('反转时间分布')
        axes[1, 1].set_xlabel('交易日')
        axes[1, 1].set_ylabel('反转次数')
        axes[1, 1].grid(True, alpha=0.3)

        # 6. 价格波动性分布
        volatilities = [r['volatility'] for r in self.simulation_results]
        axes[1, 2].hist(volatilities, bins=15, alpha=0.7, color='lightseagreen', edgecolor='black')
        axes[1, 2].set_title('价格波动性分布')
        axes[1, 2].set_xlabel('波动性')
        axes[1, 2].set_ylabel('频次')
        axes[1, 2].grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存图表
        chart_filename = f"market_reversal_analysis_{timestamp}.png"
        plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
        plt.show()

        print(f"可视化图表已保存至: {chart_filename}")
        return chart_filename

    def save_raw_data(self):
        """
        保存原始数据
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        data_filename = f"market_reversal_raw_data_{timestamp}.json"

        # 准备数据（转换numpy数组为列表）
        export_data = {
            'simulation_count': self.simulation_count,
            'simulation_results': [],
            'reversal_data': self.reversal_data
        }

        for result in self.simulation_results:
            export_result = {
                'seed': result['seed'],
                'price_history': result['price_history'],
                'volume_history': result['volume_history'],
                'value_history': result['value_history'],
                'reversals': result['reversals'],
                'total_return': result['total_return'],
                'final_price': result['final_price'],
                'max_price': result['max_price'],
                'min_price': result['min_price'],
                'volatility': result['volatility']
            }
            export_data['simulation_results'].append(export_result)

        with open(data_filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)

        print(f"原始数据已保存至: {data_filename}")
        return data_filename


def main():
    """
    主程序入口
    """
    print("=" * 60)
    print("股市趋势反转深度分析模拟器")
    print("=" * 60)

    # 获取用户输入
    try:
        simulation_count = int(input("请输入模拟次数 (建议20-100次): ") or "50")
        if simulation_count <= 0:
            simulation_count = 50
    except ValueError:
        simulation_count = 50

    print(f"\n将进行 {simulation_count} 次模拟分析...")

    # 创建模拟器
    simulator = MarketReversalSimulator(simulation_count)

    # 运行模拟
    simulator.run_simulations()

    # 生成分析报告
    print("\n正在生成分析报告...")
    report_file = simulator.generate_detailed_report()

    # 创建可视化
    print("\n正在生成可视化图表...")
    chart_file = simulator.create_visualizations()

    # 保存原始数据
    print("\n正在保存原始数据...")
    data_file = simulator.save_raw_data()

    print("\n" + "=" * 60)
    print("分析完成！生成的文件:")
    print(f"1. 分析报告: {report_file}")
    print(f"2. 可视化图表: {chart_file}")
    print(f"3. 原始数据: {data_file}")
    print("=" * 60)


if __name__ == "__main__":
    main()
