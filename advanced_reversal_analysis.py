#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级趋势反转规律分析程序
通过多次模拟运行，深度分析趋势反转的一般规律和原因

功能特点:
1. 多维度反转因素分析
2. 时间序列模式识别
3. 反转预测模型评估
4. 详细的统计分析和可视化

作者: AI Assistant
版本: 2.0
日期: 2024
"""

import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from collections import defaultdict, Counter
import json
import os
from datetime import datetime
import random
from scipy import stats
import seaborn as sns
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")
sns.set_palette("husl")

class AdvancedReversalAnalyzer:
    """
    高级反转分析器
    """
    
    def __init__(self):
        self.results = []
        self.all_reversals = []
        self.market_features = []
        self.prediction_model = None
    
    def identify_trend_reversals(self, price_history, window=3, min_change=0.01):
        """
        改进的趋势反转识别算法
        """
        reversals = []
        
        # 计算移动平均线
        ma_short = pd.Series(price_history).rolling(window=5).mean().fillna(method='bfill')
        ma_long = pd.Series(price_history).rolling(window=20).mean().fillna(method='bfill')
        
        for i in range(window, len(price_history) - window):
            current_price = price_history[i]
            
            # 检查是否为峰值
            is_peak = True
            for j in range(i - window, i + window + 1):
                if j != i and price_history[j] >= current_price:
                    is_peak = False
                    break
            
            # 检查是否为谷值
            is_trough = True
            for j in range(i - window, i + window + 1):
                if j != i and price_history[j] <= current_price:
                    is_trough = False
                    break
            
            # 验证变化幅度和趋势确认
            if is_peak:
                # 峰值确认：价格高于短期和长期均线
                if current_price > ma_short.iloc[i] and current_price > ma_long.iloc[i]:
                    max_before = max(price_history[max(0, i-10):i]) if i > 0 else current_price
                    max_after = max(price_history[i+1:min(len(price_history), i+11)]) if i < len(price_history)-1 else current_price
                    if (current_price - max_before) / max_before > min_change and \
                       (current_price - max_after) / max_after > min_change:
                        reversals.append((i, 'peak', current_price))
            
            elif is_trough:
                # 谷值确认：价格低于短期和长期均线
                if current_price < ma_short.iloc[i] and current_price < ma_long.iloc[i]:
                    min_before = min(price_history[max(0, i-10):i]) if i > 0 else current_price
                    min_after = min(price_history[i+1:min(len(price_history), i+11)]) if i < len(price_history)-1 else current_price
                    if (min_before - current_price) / min_before > min_change and \
                       (min_after - current_price) / min_after > min_change:
                        reversals.append((i, 'trough', current_price))
        
        return reversals
    
    def simulate_advanced_market(self, days=1000, seed=None):
        """
        高级市场模拟，包含更多市场因素
        """
        if seed is not None:
            np.random.seed(seed)
            random.seed(seed)
        
        # 初始化
        price_history = [100.0]
        volume_history = []
        volatility_history = [0.02]  # 波动率历史
        sentiment_history = [0.0]    # 市场情绪历史
        
        # 市场参数
        base_volatility = 0.02
        sentiment = 0.0
        momentum_factor = 0.0
        mean_reversion_strength = 0.1
        
        # 外部冲击参数
        shock_probability = 0.03
        shock_magnitude = 0.05
        
        for day in range(1, days):
            current_price = price_history[-1]
            
            # 1. 基础随机游走
            random_return = np.random.normal(0, base_volatility)
            
            # 2. 动量效应
            if day > 10:
                recent_returns = [(price_history[i] / price_history[i-1] - 1) for i in range(day-10, day)]
                momentum_factor = np.mean(recent_returns) * 0.3
            
            # 3. 均值回归效应
            if day > 50:
                long_term_avg = np.mean(price_history[-50:])
                deviation = (current_price - long_term_avg) / long_term_avg
                mean_reversion = -deviation * mean_reversion_strength
            else:
                mean_reversion = 0
            
            # 4. 市场情绪
            sentiment_change = np.random.normal(0, 0.01)
            sentiment = max(-1, min(1, sentiment + sentiment_change))
            sentiment_effect = sentiment * 0.005
            
            # 5. 波动率聚集效应
            if day > 1:
                prev_return = abs(price_history[-1] / price_history[-2] - 1)
                volatility = base_volatility * (1 + prev_return * 2)
            else:
                volatility = base_volatility
            
            volatility_history.append(volatility)
            sentiment_history.append(sentiment)
            
            # 6. 外部冲击
            if random.random() < shock_probability:
                shock = np.random.normal(0, shock_magnitude)
            else:
                shock = 0
            
            # 7. 计算总收益率
            total_return = (random_return + momentum_factor + mean_reversion + 
                          sentiment_effect + shock)
            
            # 8. 更新价格
            new_price = current_price * (1 + total_return)
            new_price = max(new_price, 10)
            new_price = min(new_price, 10000)
            
            if np.isnan(new_price) or np.isinf(new_price):
                new_price = price_history[-1]
            
            price_history.append(new_price)
            
            # 9. 计算交易量（与波动率和情绪相关）
            volume = (abs(total_return) * 1000 + abs(sentiment) * 500 + 
                     np.random.exponential(100))
            volume_history.append(volume)
        
        # 计算最终收益率
        final_return = (price_history[-1] / price_history[0] - 1) * 100
        if np.isnan(final_return) or np.isinf(final_return):
            final_return = 0.0
        
        return {
            'price_history': price_history,
            'volume_history': volume_history,
            'volatility_history': volatility_history,
            'sentiment_history': sentiment_history,
            'final_return': final_return
        }
    
    def extract_market_features(self, market_data, day):
        """
        提取市场特征用于机器学习分析
        """
        price_history = market_data['price_history']
        volume_history = market_data['volume_history']
        volatility_history = market_data['volatility_history']
        sentiment_history = market_data['sentiment_history']
        
        if day < 30:
            return None
        
        features = {}
        
        # 价格特征
        features['price'] = price_history[day]
        features['price_ma5'] = np.mean(price_history[day-5:day])
        features['price_ma20'] = np.mean(price_history[day-20:day])
        features['price_std20'] = np.std(price_history[day-20:day])
        
        # 收益率特征
        returns_5d = [(price_history[i] / price_history[i-1] - 1) for i in range(day-5, day)]
        returns_20d = [(price_history[i] / price_history[i-1] - 1) for i in range(day-20, day)]
        
        features['return_5d'] = np.mean(returns_5d)
        features['return_20d'] = np.mean(returns_20d)
        features['return_std_5d'] = np.std(returns_5d)
        features['return_std_20d'] = np.std(returns_20d)
        
        # 动量特征
        features['momentum_5d'] = price_history[day] / price_history[day-5] - 1
        features['momentum_20d'] = price_history[day] / price_history[day-20] - 1
        
        # 技术指标
        features['rsi'] = self.calculate_rsi(price_history[:day+1], period=14)
        features['bollinger_position'] = self.calculate_bollinger_position(price_history[:day+1])
        
        # 交易量特征
        features['volume'] = volume_history[day]
        features['volume_ma5'] = np.mean(volume_history[day-5:day])
        features['volume_ratio'] = volume_history[day] / np.mean(volume_history[day-20:day])
        
        # 波动率特征
        features['volatility'] = volatility_history[day]
        features['volatility_ma5'] = np.mean(volatility_history[day-5:day])
        
        # 情绪特征
        features['sentiment'] = sentiment_history[day]
        features['sentiment_ma5'] = np.mean(sentiment_history[day-5:day])
        
        return features
    
    def calculate_rsi(self, prices, period=14):
        """
        计算RSI指标
        """
        if len(prices) < period + 1:
            return 50
        
        deltas = [prices[i] - prices[i-1] for i in range(1, len(prices))]
        gains = [d if d > 0 else 0 for d in deltas[-period:]]
        losses = [-d if d < 0 else 0 for d in deltas[-period:]]
        
        avg_gain = np.mean(gains)
        avg_loss = np.mean(losses)
        
        if avg_loss == 0:
            return 100
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def calculate_bollinger_position(self, prices, period=20, std_dev=2):
        """
        计算布林带位置
        """
        if len(prices) < period:
            return 0.5
        
        ma = np.mean(prices[-period:])
        std = np.std(prices[-period:])
        
        upper_band = ma + (std_dev * std)
        lower_band = ma - (std_dev * std)
        
        current_price = prices[-1]
        
        if upper_band == lower_band:
            return 0.5
        
        position = (current_price - lower_band) / (upper_band - lower_band)
        return max(0, min(1, position))
    
    def analyze_reversal_factors_advanced(self, market_data, reversals):
        """
        高级反转因素分析
        """
        factors = []
        
        for day, reversal_type, price in reversals:
            factor_analysis = {
                'day': day,
                'type': reversal_type,
                'price': price,
                'factors': [],
                'technical_indicators': {},
                'market_conditions': {}
            }
            
            # 提取市场特征
            features = self.extract_market_features(market_data, day)
            if features is None:
                continue
            
            # 技术指标分析
            factor_analysis['technical_indicators'] = {
                'rsi': features['rsi'],
                'bollinger_position': features['bollinger_position'],
                'momentum_5d': features['momentum_5d'] * 100,
                'momentum_20d': features['momentum_20d'] * 100
            }
            
            # 市场条件分析
            factor_analysis['market_conditions'] = {
                'volatility': features['volatility'],
                'volume_ratio': features['volume_ratio'],
                'sentiment': features['sentiment']
            }
            
            # 因素识别
            if features['rsi'] > 70:
                factor_analysis['factors'].append(f"RSI超买({features['rsi']:.1f})")
            elif features['rsi'] < 30:
                factor_analysis['factors'].append(f"RSI超卖({features['rsi']:.1f})")
            
            if features['bollinger_position'] > 0.9:
                factor_analysis['factors'].append("价格接近布林带上轨")
            elif features['bollinger_position'] < 0.1:
                factor_analysis['factors'].append("价格接近布林带下轨")
            
            if abs(features['momentum_5d']) > 0.1:
                direction = "上涨" if features['momentum_5d'] > 0 else "下跌"
                factor_analysis['factors'].append(f"短期强势{direction}({features['momentum_5d']*100:.1f}%)")
            
            if features['volume_ratio'] > 2:
                factor_analysis['factors'].append(f"交易量异常放大({features['volume_ratio']:.1f}倍)")
            elif features['volume_ratio'] < 0.5:
                factor_analysis['factors'].append(f"交易量萎缩({features['volume_ratio']:.1f}倍)")
            
            if features['volatility'] > 0.05:
                factor_analysis['factors'].append(f"高波动率({features['volatility']*100:.1f}%)")
            
            if abs(features['sentiment']) > 0.5:
                mood = "乐观" if features['sentiment'] > 0 else "悲观"
                factor_analysis['factors'].append(f"市场情绪{mood}({features['sentiment']:.2f})")
            
            factors.append(factor_analysis)
        
        return factors
    
    def run_multiple_simulations(self, num_simulations=50, days=1000):
        """
        运行多次高级模拟分析
        """
        print(f"开始运行 {num_simulations} 次高级模拟，每次 {days} 天...")
        
        self.results = []
        self.all_reversals = []
        self.market_features = []
        
        for i in range(num_simulations):
            seed = 2000 + i
            print(f"运行模拟 {i+1}/{num_simulations} (种子: {seed})")
            
            # 运行模拟
            market_data = self.simulate_advanced_market(days=days, seed=seed)
            
            # 识别反转点
            reversals = self.identify_trend_reversals(market_data['price_history'])
            
            # 分析反转因素
            factors = self.analyze_reversal_factors_advanced(market_data, reversals)
            
            # 提取所有市场特征用于机器学习
            for day in range(30, days-10):
                features = self.extract_market_features(market_data, day)
                if features is not None:
                    # 标记是否在未来10天内发生反转
                    future_reversals = [r for r in reversals if day < r[0] <= day + 10]
                    features['will_reverse'] = len(future_reversals) > 0
                    features['simulation'] = i + 1
                    self.market_features.append(features)
            
            # 保存结果
            result = {
                'simulation': i + 1,
                'seed': seed,
                'final_return': market_data['final_return'],
                'reversal_count': len(reversals),
                'reversals': reversals,
                'factors': factors,
                'market_stats': {
                    'avg_volatility': np.mean(market_data['volatility_history']),
                    'avg_sentiment': np.mean(market_data['sentiment_history']),
                    'max_price': max(market_data['price_history']),
                    'min_price': min(market_data['price_history'])
                }
            }
            
            self.results.append(result)
            self.all_reversals.extend(reversals)
        
        return self.results
    
    def train_prediction_model(self):
        """
        训练反转预测模型
        """
        if not self.market_features:
            print("没有足够的特征数据来训练模型")
            return None
        
        print("训练反转预测模型...")
        
        # 准备数据
        df = pd.DataFrame(self.market_features)
        
        # 选择特征
        feature_columns = ['price_ma5', 'price_ma20', 'return_5d', 'return_20d', 
                          'momentum_5d', 'momentum_20d', 'rsi', 'bollinger_position',
                          'volume_ratio', 'volatility', 'sentiment']
        
        X = df[feature_columns].fillna(0)
        y = df['will_reverse']
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
        
        # 训练模型
        self.prediction_model = RandomForestClassifier(n_estimators=100, random_state=42)
        self.prediction_model.fit(X_train, y_train)
        
        # 评估模型
        y_pred = self.prediction_model.predict(X_test)
        
        print("\n模型评估结果:")
        print(classification_report(y_test, y_pred))
        
        # 特征重要性
        feature_importance = pd.DataFrame({
            'feature': feature_columns,
            'importance': self.prediction_model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        print("\n特征重要性排序:")
        for _, row in feature_importance.head(10).iterrows():
            print(f"{row['feature']}: {row['importance']:.4f}")
        
        return {
            'model': self.prediction_model,
            'feature_importance': feature_importance,
            'test_accuracy': (y_pred == y_test).mean()
        }
    
    def create_advanced_visualizations(self):
        """
        创建高级可视化图表
        """
        fig = plt.figure(figsize=(20, 15))
        
        # 创建子图布局
        gs = fig.add_gridspec(4, 3, hspace=0.3, wspace=0.3)
        
        # 1. 反转频率分布
        ax1 = fig.add_subplot(gs[0, 0])
        reversal_counts = [r['reversal_count'] for r in self.results]
        ax1.hist(reversal_counts, bins=15, alpha=0.7, color='skyblue', edgecolor='black')
        ax1.set_title('反转频率分布', fontsize=12, fontweight='bold')
        ax1.set_xlabel('每次模拟的反转次数')
        ax1.set_ylabel('频次')
        
        # 2. 收益率vs反转次数散点图
        ax2 = fig.add_subplot(gs[0, 1])
        returns = [r['final_return'] for r in self.results if not np.isnan(r['final_return'])]
        counts = [r['reversal_count'] for r in self.results if not np.isnan(r['final_return'])]
        ax2.scatter(counts, returns, alpha=0.6, color='coral')
        ax2.set_title('收益率 vs 反转次数', fontsize=12, fontweight='bold')
        ax2.set_xlabel('反转次数')
        ax2.set_ylabel('收益率 (%)')
        
        # 添加趋势线
        if len(counts) > 1:
            z = np.polyfit(counts, returns, 1)
            p = np.poly1d(z)
            ax2.plot(counts, p(counts), "r--", alpha=0.8)
        
        # 3. RSI分布在反转点
        ax3 = fig.add_subplot(gs[0, 2])
        rsi_values = []
        for result in self.results:
            for factor in result['factors']:
                if 'technical_indicators' in factor:
                    rsi_values.append(factor['technical_indicators']['rsi'])
        
        if rsi_values:
            ax3.hist(rsi_values, bins=20, alpha=0.7, color='lightgreen', edgecolor='black')
            ax3.axvline(30, color='red', linestyle='--', label='超卖线')
            ax3.axvline(70, color='red', linestyle='--', label='超买线')
            ax3.set_title('反转点RSI分布', fontsize=12, fontweight='bold')
            ax3.set_xlabel('RSI值')
            ax3.set_ylabel('频次')
            ax3.legend()
        
        # 4. 波动率vs反转频率
        ax4 = fig.add_subplot(gs[1, 0])
        volatilities = [r['market_stats']['avg_volatility'] for r in self.results]
        ax4.scatter(volatilities, reversal_counts, alpha=0.6, color='purple')
        ax4.set_title('平均波动率 vs 反转频率', fontsize=12, fontweight='bold')
        ax4.set_xlabel('平均波动率')
        ax4.set_ylabel('反转次数')
        
        # 5. 情绪vs收益率
        ax5 = fig.add_subplot(gs[1, 1])
        sentiments = [r['market_stats']['avg_sentiment'] for r in self.results]
        valid_returns = [r['final_return'] for r in self.results if not np.isnan(r['final_return'])]
        valid_sentiments = [sentiments[i] for i, r in enumerate(self.results) if not np.isnan(r['final_return'])]
        
        if valid_sentiments:
            ax5.scatter(valid_sentiments, valid_returns, alpha=0.6, color='orange')
            ax5.set_title('平均市场情绪 vs 收益率', fontsize=12, fontweight='bold')
            ax5.set_xlabel('平均市场情绪')
            ax5.set_ylabel('收益率 (%)')
        
        # 6. 反转类型时间分布
        ax6 = fig.add_subplot(gs[1, 2])
        peak_days = [r[0] for r in self.all_reversals if r[1] == 'peak']
        trough_days = [r[0] for r in self.all_reversals if r[1] == 'trough']
        
        ax6.hist([peak_days, trough_days], bins=20, alpha=0.7, 
                label=['峰值反转', '谷值反转'], color=['red', 'blue'])
        ax6.set_title('反转点时间分布', fontsize=12, fontweight='bold')
        ax6.set_xlabel('模拟天数')
        ax6.set_ylabel('反转次数')
        ax6.legend()
        
        # 7. 特征重要性（如果模型已训练）
        if self.prediction_model is not None:
            ax7 = fig.add_subplot(gs[2, :])
            feature_names = ['价格MA5', '价格MA20', '5日收益', '20日收益', 
                           '5日动量', '20日动量', 'RSI', '布林位置',
                           '成交量比', '波动率', '市场情绪']
            importances = self.prediction_model.feature_importances_
            
            bars = ax7.barh(range(len(feature_names)), importances, color='lightblue')
            ax7.set_yticks(range(len(feature_names)))
            ax7.set_yticklabels(feature_names)
            ax7.set_title('反转预测特征重要性', fontsize=12, fontweight='bold')
            ax7.set_xlabel('重要性得分')
            
            # 添加数值标签
            for i, bar in enumerate(bars):
                width = bar.get_width()
                ax7.text(width, bar.get_y() + bar.get_height()/2, 
                        f'{width:.3f}', ha='left', va='center')
        
        # 8. 反转幅度分布
        ax8 = fig.add_subplot(gs[3, 0])
        reversal_magnitudes = []
        for result in self.results:
            for day, _, price in result['reversals']:
                if day < len(result['reversals']):
                    magnitude = abs(price - 100) / 100
                    reversal_magnitudes.append(magnitude)
        
        if reversal_magnitudes:
            ax8.hist(reversal_magnitudes, bins=20, alpha=0.7, color='gold', edgecolor='black')
            ax8.set_title('反转幅度分布', fontsize=12, fontweight='bold')
            ax8.set_xlabel('反转幅度 (相对于起始价格)')
            ax8.set_ylabel('频次')
        
        # 9. 主要反转因素词云风格图
        ax9 = fig.add_subplot(gs[3, 1:])
        factor_counter = Counter()
        for result in self.results:
            for factor_analysis in result['factors']:
                for factor in factor_analysis['factors']:
                    factor_counter[factor] += 1
        
        if factor_counter:
            factors = list(factor_counter.keys())[:15]
            counts = list(factor_counter.values())[:15]
            
            # 创建水平条形图
            bars = ax9.barh(range(len(factors)), counts, color=plt.cm.viridis(np.linspace(0, 1, len(factors))))
            ax9.set_yticks(range(len(factors)))
            ax9.set_yticklabels([f[:20] + '...' if len(f) > 20 else f for f in factors])
            ax9.set_title('主要反转因素排名', fontsize=12, fontweight='bold')
            ax9.set_xlabel('出现次数')
            
            # 添加数值标签
            for i, bar in enumerate(bars):
                width = bar.get_width()
                ax9.text(width, bar.get_y() + bar.get_height()/2, 
                        f'{int(width)}', ha='left', va='center')
        
        plt.suptitle('高级趋势反转规律分析可视化报告', fontsize=16, fontweight='bold')
        
        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        chart_filename = f"advanced_reversal_analysis_{timestamp}.png"
        plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
        print(f"\n高级可视化图表已保存到: {chart_filename}")
        
        return chart_filename

def main():
    """
    主函数
    """
    print("=" * 80)
    print("高级趋势反转规律分析程序")
    print("=" * 80)
    
    # 创建分析器
    analyzer = AdvancedReversalAnalyzer()
    
    # 获取用户输入
    try:
        num_simulations = int(input("请输入模拟次数 (建议30-100次): ") or "50")
        simulation_days = int(input("请输入每次模拟天数 (建议1000-2000天): ") or "1000")
    except ValueError:
        print("输入无效，使用默认值")
        num_simulations = 50
        simulation_days = 1000
    
    print(f"\n配置: {num_simulations}次模拟，每次{simulation_days}天")
    print("开始高级分析...\n")
    
    # 运行模拟
    results = analyzer.run_multiple_simulations(
        num_simulations=num_simulations, 
        days=simulation_days
    )
    
    # 训练预测模型
    model_results = analyzer.train_prediction_model()
    
    # 创建高级可视化
    chart_filename = analyzer.create_advanced_visualizations()
    
    # 生成详细报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 计算统计数据
    reversal_counts = [r['reversal_count'] for r in results]
    returns = [r['final_return'] for r in results if not np.isnan(r['final_return'])]
    
    # 生成报告
    report = f"""
================================================================================
高级趋势反转规律分析报告
================================================================================

一、模拟概况
----------------------------------------
模拟次数: {len(results)}次
模拟天数: {simulation_days}天/次
总数据点: {len(analyzer.market_features)}个

二、反转频率统计
----------------------------------------
平均反转次数: {np.mean(reversal_counts):.2f}次/模拟
反转频率标准差: {np.std(reversal_counts):.2f}
反转频率中位数: {np.median(reversal_counts):.2f}
最少反转次数: {np.min(reversal_counts)}次
最多反转次数: {np.max(reversal_counts)}次

三、市场表现分析
----------------------------------------
平均收益率: {np.mean(returns):.2f}%
收益率标准差: {np.std(returns):.2f}%
收益率中位数: {np.median(returns):.2f}%
正收益模拟比例: {sum(1 for r in returns if r > 0) / len(returns) * 100:.1f}%

四、反转类型分析
----------------------------------------
总反转点数: {len(analyzer.all_reversals)}个
峰值反转: {len([r for r in analyzer.all_reversals if r[1] == 'peak'])}个
谷值反转: {len([r for r in analyzer.all_reversals if r[1] == 'trough'])}个
峰谷比例: {len([r for r in analyzer.all_reversals if r[1] == 'peak']) / max(len([r for r in analyzer.all_reversals if r[1] == 'trough']), 1):.2f}

五、预测模型性能
----------------------------------------
"""
    
    if model_results:
        report += f"""模型准确率: {model_results['test_accuracy']:.3f}
最重要特征: {model_results['feature_importance'].iloc[0]['feature']}
特征重要性得分: {model_results['feature_importance'].iloc[0]['importance']:.4f}
"""
    else:
        report += "预测模型训练失败\n"
    
    # 添加主要发现
    factor_counter = Counter()
    for result in results:
        for factor_analysis in result['factors']:
            for factor in factor_analysis['factors']:
                factor_counter[factor] += 1
    
    report += f"""

六、主要反转因素 (前10名)
----------------------------------------
"""
    
    for i, (factor, count) in enumerate(factor_counter.most_common(10)):
        percentage = count / max(len(analyzer.all_reversals), 1) * 100
        report += f"{i+1:2d}. {factor}: {count}次 ({percentage:.1f}%)\n"
    
    # 相关性分析
    if len(returns) > 1 and len(reversal_counts) > 1:
        correlation = np.corrcoef(returns, reversal_counts)[0, 1]
        report += f"""

七、相关性分析
----------------------------------------
收益率与反转频率相关性: {correlation:.3f}
"""
        
        if abs(correlation) > 0.3:
            direction = "正" if correlation > 0 else "负"
            report += f"发现{direction}相关关系，相关性较强\n"
        else:
            report += "收益率与反转频率相关性较弱\n"
    
    report += f"""

八、关键发现总结
----------------------------------------
1. 反转频率: 平均每次模拟发生{np.mean(reversal_counts):.1f}次反转
2. 反转偏好: {'谷值反转更频繁' if len([r for r in analyzer.all_reversals if r[1] == 'trough']) > len([r for r in analyzer.all_reversals if r[1] == 'peak']) else '峰值反转更频繁'}
3. 市场表现: {'整体收益为正' if np.mean(returns) > 0 else '整体收益为负'}，平均收益{np.mean(returns):.1f}%
4. 主要因素: {factor_counter.most_common(1)[0][0] if factor_counter else '无明显因素'}
"""
    
    if model_results and model_results['test_accuracy'] > 0.6:
        report += f"5. 预测能力: 机器学习模型显示反转具有一定可预测性(准确率{model_results['test_accuracy']:.1f})\n"
    else:
        report += "5. 预测能力: 反转预测具有较大随机性\n"
    
    report += "\n" + "=" * 80 + "\n"
    
    # 保存报告
    report_filename = f"advanced_reversal_report_{timestamp}.txt"
    with open(report_filename, 'w', encoding='utf-8') as f:
        f.write(report)
    
    # 保存详细数据
    def convert_numpy_types(obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {key: convert_numpy_types(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy_types(item) for item in obj]
        return obj
    
    data_filename = f"advanced_reversal_data_{timestamp}.json"
    with open(data_filename, 'w', encoding='utf-8') as f:
        json.dump({
            'summary_stats': {
                'simulation_count': len(results),
                'avg_reversals': float(np.mean(reversal_counts)),
                'avg_return': float(np.mean(returns)),
                'total_reversals': len(analyzer.all_reversals)
            },
            'model_performance': model_results['test_accuracy'] if model_results else None,
            'top_factors': dict(factor_counter.most_common(20))
        }, f, ensure_ascii=False, indent=2)
    
    # 显示结果
    print("\n" + "=" * 80)
    print("高级分析完成！")
    print("=" * 80)
    print(report)
    
    print(f"\n生成文件:")
    print(f"- 分析报告: {report_filename}")
    print(f"- 可视化图表: {chart_filename}")
    print(f"- 数据摘要: {data_filename}")

if __name__ == "__main__":
    main()